import random
from playcard import make_deck, is_red, get_rank, RANK_MAP, SUITS

# Return two integers a<b in range(n), assuming n>=2
def rand_int_pair(n):
    a = random.randint(0,n-1)
    b = (a + random.randint(1, n-1))%n
    return (a,b) if a<b else (b,a)

def new_game(session):
    # Create a standard deck of 52 cards
    deck = make_deck()
    random.shuffle(deck)
    tableau = [[] for _ in range(8)]
    for i, card in enumerate(deck):
        tableau[i % 8].append(card)

    # make the deal easier because it is often difficult
    for i in range(8):
        column = tableau[i]
        n = 7 if i<4 else 6
        for _ in range(random.randint(2,3)):
            a, b = rand_int_pair(n)
            if get_rank(column[a]) < get_rank(column[b]):
                column[a], column[b] = column[b], column[a]

    session['game_state'] = {
        'cells': [[] for _ in range(4)],
        'piles': [[] for _ in range(4)],
        'tableau': tableau,
        'game_over': False,
        'message': '',
    }
    return


# Return (is_red, rank) for a card
def color_and_rank(card):
    return is_red(card), get_rank(card)

# Convert string to int, return -1 if conversion fails
def safe_int(digit):
    try:
        return int(digit)
    except ValueError:
        return -1



def game_update(session, action):
    game_state = session.get('game_state', {})
    if not game_state:
        return new_game(session)

    cells = game_state['cells']
    piles = game_state['piles']  # foundations
    tableau = game_state['tableau']

    # Clear previous message
    game_state['message'] = ''
    game_state['message_class'] = ''

    # Helper function to check if a sequence is valid (alternating colors, descending ranks)
    def is_valid_sequence(cards):
        if len(cards) <= 1:
            return True
        for i in range(len(cards) - 1):
            curr_red, curr_rank = color_and_rank(cards[i])
            next_red, next_rank = color_and_rank(cards[i + 1])
            if curr_red == next_red or curr_rank != next_rank + 1:
                return False
        return True

    # Helper function to count free spaces
    def count_free_spaces():
        free_cells = sum(1 for cell in cells if not cell)
        free_columns = sum(1 for column in tableau if not column)
        return free_cells, free_columns

    # Helper function to calculate max movable cards
    def max_movable_cards(free_cells, free_columns, target_empty=False):
        # If target is not empty, we need to exclude it from free columns
        if not target_empty:
            free_columns = max(0, free_columns)
        else:
            free_columns = max(0, free_columns - 1)  # Target column doesn't count
        return (free_cells + 1) * (2 ** free_columns)

    # - 'tt<source><target>': Move card(s) from column <source> to column <target> (0-based)
    if action.startswith('tt') and len(action) == 4:
        source_col = safe_int(action[2])
        target_col = safe_int(action[3])

        if source_col < 0 or source_col >= 8 or target_col < 0 or target_col >= 8 or source_col == target_col:
            game_state['message'] = 'Invalid column indices.'
            return

        source_column = tableau[source_col]
        target_column = tableau[target_col]

        if not source_column:
            game_state['message'] = 'No cards to move from source column.'
            return

        # Find the longest valid sequence from the bottom of source column
        valid_sequence = []
        for i in range(len(source_column) - 1, -1, -1):
            temp_sequence = source_column[i:]
            if is_valid_sequence(temp_sequence):
                valid_sequence = temp_sequence
            else:
                break

        if not valid_sequence:
            game_state['message'] = 'No valid sequence to move.'
            return

        # Check if we can place the sequence on target
        if target_column:
            # Target is not empty - check if top card of sequence can go on target
            target_top_red, target_top_rank = color_and_rank(target_column[-1])
            sequence_bottom_red, sequence_bottom_rank = color_and_rank(valid_sequence[0])

            if target_top_red == sequence_bottom_red or target_top_rank != sequence_bottom_rank + 1:
                game_state['message'] = 'Card(s) not movable.'
                return

            # Check space constraints
            free_cells, free_columns = count_free_spaces()
            max_cards = max_movable_cards(free_cells, free_columns, target_empty=False)

            if len(valid_sequence) > max_cards:
                game_state['message'] = f'Your free slots only support moving {max_cards} cards while {len(valid_sequence)} are desired. No card was moved.'
                return

            # Move the entire valid sequence
            for _ in range(len(valid_sequence)):
                target_column.append(source_column.pop())
            game_state['message'] = 'Card moved between tableau columns.'

        else:
            # Target is empty - only move one card to avoid disadvantaging player
            target_column.append(source_column.pop())
            game_state['message'] = 'Card moved between tableau columns.'

    # - 'tc<source><target>': Move card from column <source> to cell <target> (0-based)
    elif action.startswith('tc') and len(action) == 4:
        source_col = safe_int(action[2])
        target_cell = safe_int(action[3])

        if source_col < 0 or source_col >= 8 or target_cell < 0 or target_cell >= 4:
            game_state['message'] = 'Invalid indices.'
            return

        source_column = tableau[source_col]
        target_cell_list = cells[target_cell]

        if not source_column:
            game_state['message'] = 'No cards to move from source column.'
            return

        if target_cell_list:
            game_state['message'] = 'Target cell is not empty.'
            return

        # Move one card from tableau to cell
        target_cell_list.append(source_column.pop())
        game_state['message'] = 'Card moved from tableau to cell.'

    # - 'ct<source><target>': Move card from cell <source> to column <target> (0-based)
    elif action.startswith('ct') and len(action) == 4:
        source_cell = safe_int(action[2])
        target_col = safe_int(action[3])

        if source_cell < 0 or source_cell >= 4 or target_col < 0 or target_col >= 8:
            game_state['message'] = 'Invalid indices.'
            return

        source_cell_list = cells[source_cell]
        target_column = tableau[target_col]

        if not source_cell_list:
            game_state['message'] = 'No card to move from source cell.'
            return

        card = source_cell_list[0]

        if target_column:
            # Check if card can be placed on target column
            target_top_red, target_top_rank = color_and_rank(target_column[-1])
            card_red, card_rank = color_and_rank(card)

            if target_top_red == card_red or target_top_rank != card_rank + 1:
                game_state['message'] = 'Card(s) not movable.'
                return

        # Move card from cell to tableau
        target_column.append(source_cell_list.pop())
        game_state['message'] = 'Card moved from cell to tableau.'

    # - 'tf<column>': Move last card from column <column> (0-based) to foundation
    elif action.startswith('tf') and len(action) == 3:
        source_col = safe_int(action[2])

        if source_col < 0 or source_col >= 8:
            game_state['message'] = 'Invalid column index.'
            return

        source_column = tableau[source_col]

        if not source_column:
            game_state['message'] = 'No cards to move from source column.'
            return

        card = source_column[-1]
        card_suit = card[1]
        card_rank = get_rank(card)

        # Find the appropriate foundation pile (by suit)
        suit_index = SUITS.index(card_suit)
        foundation_pile = piles[suit_index]

        # Check if card can be placed on foundation
        if not foundation_pile:
            # Foundation is empty, only Ace can be placed
            if card_rank != 1:
                game_state['message'] = 'Card does not meet foundation requirements.'
                return
        else:
            # Foundation has cards, check if this card is next in sequence
            top_card_rank = get_rank(foundation_pile[-1])
            if card_rank != top_card_rank + 1:
                game_state['message'] = 'Card does not meet foundation requirements.'
                return

        # Move card to foundation
        foundation_pile.append(source_column.pop())
        game_state['message'] = 'Card moved to foundation.'

        # Check for win condition (simple version - all cards in foundations)
        total_foundation_cards = sum(len(pile) for pile in piles)
        if total_foundation_cards == 52:
            game_state['game_over'] = True
            game_state['message'] = 'Congratulations! You won!'

    # - 'cf<cell>': Move card from cell <cell> (0-based) to foundation
    elif action.startswith('cf') and len(action) == 3:
        source_cell = safe_int(action[2])

        if source_cell < 0 or source_cell >= 4:
            game_state['message'] = 'Invalid cell index.'
            return

        source_cell_list = cells[source_cell]

        if not source_cell_list:
            game_state['message'] = 'No card to move from source cell.'
            return

        card = source_cell_list[0]
        card_suit = card[1]
        card_rank = get_rank(card)

        # Find the appropriate foundation pile (by suit)
        suit_index = SUITS.index(card_suit)
        foundation_pile = piles[suit_index]

        # Check if card can be placed on foundation
        if not foundation_pile:
            # Foundation is empty, only Ace can be placed
            if card_rank != 1:
                game_state['message'] = 'Card does not meet foundation requirements.'
                return
        else:
            # Foundation has cards, check if this card is next in sequence
            top_card_rank = get_rank(foundation_pile[-1])
            if card_rank != top_card_rank + 1:
                game_state['message'] = 'Card does not meet foundation requirements.'
                return

        # Move card to foundation
        foundation_pile.append(source_cell_list.pop())
        game_state['message'] = 'Card moved to foundation.'

        # Check for win condition (simple version - all cards in foundations)
        total_foundation_cards = sum(len(pile) for pile in piles)
        if total_foundation_cards == 52:
            game_state['game_over'] = True
            game_state['message'] = 'Congratulations! You won!'

    else:
        game_state['message'] = 'Invalid action.'

    # game state has changed itself so tell session it has changed
    session.modified = True
    return

