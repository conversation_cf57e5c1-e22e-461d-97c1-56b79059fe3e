{% extends "base.html" %}

{% block head %}
    <link rel="stylesheet" href="/static/blackjack.css">
    <link rel="preload" href="/static/svg-cards.svg" as="image" type="image/svg+xml">
{% endblock %}

{% block main_content %}
    <div class="game-area">
        <h1>二十一点</h1>
        <div class="dealer-area">
            <h2>庄家手牌 ({{ game_state['dealer_value'] }})</h2>
            <div class="cards">
                {% if game_state['game_over'] %}
                    {% for card in game_state['dealer_hand'] %}
                        <svg class="card" viewBox="0 0 169 244">
                            <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                        </svg>
                    {% endfor %}
                {% else %}
                    <svg class="card" viewBox="0 0 169 244">
                        <use href="/static/svg-cards.svg#{{ get_card_name(game_state['dealer_hand'][0]) }}"/>
                    </svg>
                    <svg class="card" viewBox="0 0 169 244">
                        <use href="/static/svg-cards.svg#back"/>
                    </svg>
                {% endif %}
            </div>
        </div>
        <div class="player-area">
            <h2>您的手牌 ({{ game_state['player_value'] }})</h2>
            <div class="cards">
                {% for card in game_state['player_hand'] %}
                    <svg class="card" viewBox="0 0 169 244">
                        <use href="/static/svg-cards.svg#{{ get_card_name(card) }}"/>
                    </svg>
                {% endfor %}
            </div>
        </div>

        {% if game_state['message'] %}
            <div class="message {{ game_state['message_class'] }}">
                <h3>{{ game_state['message'] }}</h3>
            </div>
        {% endif %}

        <div class="actions">
            {% if not game_state['game_over'] %}
                <a href="{{ url_for('game_update', action='hit') }}" class="btn">要牌</a>
                <a href="{{ url_for('game_update', action='stand') }}" class="btn">停牌</a>
            {% endif %}
        </div>
    </div>
{% endblock %}