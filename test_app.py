#!/usr/bin/env python3

# Test script to verify Flask app functionality
import requests
import sys

def test_endpoint(url, expected_content=None):
    try:
        response = requests.get(url)
        print(f"Testing {url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if expected_content:
                if expected_content in content:
                    print(f"✓ Found expected content: {expected_content}")
                else:
                    print(f"✗ Expected content not found: {expected_content}")
                    print("First 500 chars of response:")
                    print(content[:500])
            else:
                print("✓ Request successful")
        else:
            print(f"✗ Request failed with status {response.status_code}")
        
        print("-" * 50)
        return response.status_code == 200
        
    except Exception as e:
        print(f"✗ Error testing {url}: {e}")
        print("-" * 50)
        return False

def main():
    base_url = "http://127.0.0.1:80"
    
    print("Testing Flask application endpoints...")
    print("=" * 50)
    
    # Test main page (should redirect)
    test_endpoint(f"{base_url}/")
    
    # Test select page
    test_endpoint(f"{base_url}/select", "选择游戏")
    
    # Test rules page (without game selected)
    test_endpoint(f"{base_url}/rules")
    
    # Test log page
    test_endpoint(f"{base_url}/log", "暂无游戏日志")
    
    # Test about page
    test_endpoint(f"{base_url}/about", "关于游戏中心")
    
    print("Testing complete!")

if __name__ == "__main__":
    main()
