{% extends "base.html" %}

{% block main_content %}
    <div class="text-box">
        {% if cur_game=="blackjack" %}
            <h2>二十一点游戏规则：</h2>
            <ul>
                <li>游戏使用标准的52张牌。</li>
                <li>游戏开始时，每位玩家获得两张牌。</li>
                <li>庄家最初只显示一张明牌。</li>
                <li>您可以选择要更多牌（要牌）或保持当前手牌（停牌）。</li>
                <li>如果您的点数超过21点，您立即输掉游戏（爆牌）。</li>
                <li>庄家必须要牌直到达到至少17点。</li>
                <li>如果庄家爆牌，您获胜。</li>
                <li>如果双方都没有爆牌，点数较高的一方获胜。</li>
            </ul>
        {% elif cur_game=="freecell" %}
            <h2>空当接龙游戏规则</h2>
            <p>空当接龙是一种流行的纸牌游戏，游戏开始时所有牌都面朝上发放。游戏目标是按照特定的移动规则，将所有牌移动到四个基础堆中。</p>

            <h3>游戏目标：</h3>
            <p>空当接龙的目标是将所有52张牌移动到基础堆中，按花色组织，从A到K，同时遵循游戏的移动规则。</p>

            <h3>游戏设置：</h3>
            <ul>
                <li><span class="item-title">牌组：</span> 游戏使用标准的52张牌。</li>
                <li><span class="item-title">列区：</span> 游戏板由八个列区组成，每个列区最初放置一组牌。</li>
                <li><span class="item-title">基础堆：</span> 四个基础堆位于右上角，开始时为空。牌将按升序移动到这里，从每种花色的A开始。</li>
                <li><span class="item-title">空闲单元格：</span> 有四个空闲单元格可用，每个单元格可以临时放置一张牌，以协助移动其他牌。</li>
            </ul>

            <h3>移动规则：</h3>
            <ul>
                <li><span class="item-title">列区到列区：</span> 一张牌可以从一个列区移动到另一个列区，前提是它被放在颜色相反且大一个等级的牌上。例如，红色6可以放在黑色7上。</li>
                <li><span class="item-title">牌到基础堆：</span> 一张牌可以移动到基础堆，如果它是序列中的下一张牌，从A开始。例如，红桃2可以放在红桃A上。</li>
                <li><span class="item-title">牌到空闲单元格：</span> 一张牌可以放在空闲单元格中，但每个空闲单元格一次只能放一张牌。</li>
                <li><span class="item-title">移动多张牌：</span> 多张牌可以一起移动，如果它们按降序排列且颜色交替放在有效的列区上。例如，红色8、黑色7和红色6的序列可以作为一组移动。</li>
            </ul>

            <h3>获胜条件：</h3>
            <ul>
                <li><span class="item-title">目标：</span> 当所有牌都按花色从A到K的顺序放入基础堆时，游戏获胜。</li>
                <li><span class="item-title">无可移动牌：</span> 如果没有更多有效移动，游戏失败。</li>
            </ul>

            <h3>附加说明：</h3>
            <ul>
                <li><span class="item-title">撤销：</span> 在大多数版本中，玩家可以撤销移动来纠正错误，但这取决于游戏的具体实现。</li>
                <li><span class="item-title">策略：</span> 提前规划移动很重要，特别是有效管理空闲单元格和列区。</li>
            </ul>
        {% endif %}
    </div>
{% endblock %}
