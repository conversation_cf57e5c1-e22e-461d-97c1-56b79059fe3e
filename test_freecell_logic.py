#!/usr/bin/env python3

# Test script to verify freecell logic
from playcard import is_red, get_rank

def color_and_rank(card):
    return is_red(card), get_rank(card)

# Test case: black 5 on red 6
target_card = '6H'  # Red 6 (Hearts)
source_card = '5S'  # Black 5 (Spades)

target_top_red, target_top_rank = color_and_rank(target_card)
sequence_bottom_red, sequence_bottom_rank = color_and_rank(source_card)

print(f"Target card: {target_card}")
print(f"  - is_red: {target_top_red}")
print(f"  - rank: {target_top_rank}")

print(f"Source card: {source_card}")
print(f"  - is_red: {sequence_bottom_red}")
print(f"  - rank: {sequence_bottom_rank}")

# Current logic
condition = target_top_red == sequence_bottom_red or target_top_rank != sequence_bottom_rank + 1
print(f"\nCurrent condition: {target_top_red} == {sequence_bottom_red} or {target_top_rank} != {sequence_bottom_rank} + 1")
print(f"  = {target_top_red == sequence_bottom_red} or {target_top_rank != sequence_bottom_rank + 1}")
print(f"  = {condition}")

if condition:
    print("Result: Move NOT allowed (牌不能移动)")
else:
    print("Result: Move allowed")

print("\nExpected: Move should be allowed (black 5 on red 6)")
