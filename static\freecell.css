/* Top row: cells and foundation piles */
.cells_and_piles {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.cells, .piles {
    display: flex;
    gap: 10px;
}

.cell, .pile {
    width: 10vw;
    height: calc(10vw * 244 / 169); /* Maintain SVG card aspect ratio */
    background-color: rgba(0, 0, 0, 0.2); /* Slight shadow for depth */
    border: 2px dashed #fff; /* Dashed border for empty slots */
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; /* For positioning suit images */
}

/* Remove dashed border when card is present */
.cell:has(.card), .pile:has(.card) {
    border: none;
}

/* Empty slot dimensions */
.empty-slot {
    width: 10vw;
    height: calc(10vw * 244 / 169);
}

/* Suit images for foundation piles */
.pile {
    background-size: contain; /* Scale to fit within the pile */
    background-position: center;
    background-repeat: no-repeat;
}
.pile0 {
    background-image: url('/static/heart.svg');
}

.pile1 {
    background-image: url('/static/spade.svg');
}

.pile2 {
    background-image: url('/static/diamond.svg');
}

.pile3 {
    background-image: url('/static/club.svg');
}

/* Ensure suit image is behind the card */
.pile:has(.card) {
    background-image: none; /* Hide suit image when a card is present */
}

.tableau {
    display: flex; /* Horizontal arrangement of columns */
    gap: calc(1.2vw * var(--gap-to-margin-ratio) / 2); /* Scales with margin, ~0.6vw at ratio 1 */
    justify-content: center; /* Centers columns horizontally */
    width: 100%; /* Ensures tableau spans full viewport width */
    box-sizing: border-box; /* Prevents padding/margins from breaking layout */
    margin-left: 1.2vw; /* Matches desired gap size */
    margin-right: 1.2vw; /* Matches desired gap size */
    --gap-to-margin-ratio: 1.6; /* Adjust to make gaps equal margins */
}

.tableau-column {
    display: inline-block; /* Matches original horizontal layout */
    vertical-align: top; /* Aligns columns at the top */
    width: calc(10vw + 1.2vw * var(--gap-to-margin-ratio) / 2); /* Card width + padding */
}

.tableau-column .card {
    width: 10vw; /* Responsive width, adjust as needed */
    height: auto; /* Maintains aspect ratio (169:244) */
    display: block; /* Stacks cards vertically */
    margin: 0;
    padding: 0;
    margin-top: calc(-10vw * 244 / 169 * 73 / 100); /* 2/3 overlap, scales with width */
    padding-right: calc(1.2vw * var(--gap-to-margin-ratio) / 2); /* Scales with margin */
}

.tableau-column .card:first-child {
    margin-top: 0; /* First card starts at the top */
    padding-right: calc(1.2vw * var(--gap-to-margin-ratio) / 2); /* Scales with margin */
}

.tableau {
    --arrow-offset-x: -0.5vw; /* Adjust as needed, e.g., -2px to shift left, 2px to shift right */
}

.highlight-arrow {
    position: absolute;
    width: 4vw; /* Adjust based on SVG size relative to card (10vw) */
    height: auto; /* Maintains aspect ratio */
    z-index: 10; /* Ensures arrow is above cards */
    display: none; /* Hidden by default */
    pointer-events: none; /* Prevents arrow from capturing clicks */
    transform: translateX(calc(-50% + var(--arrow-offset-x, 0px))); /* Center with adjustable offset */
}

/* Hover effect
.tableau-column .card:hover {
    padding-right: 0;
    padding-left: calc(1.2vw * var(--gap-to-margin-ratio) / 2);
}*/

/* Message */
.message {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    margin: 30px 0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.6);
    border: 3px solid;
}

.message h3 {
    font-size: 2.8rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Success messages - green background */
.message.success {
    background-color: rgba(34, 139, 34, 0.9); /* Forest green background */
    border-color: #228B22;
    color: white;
}

/* Error messages - red background */
.message.error {
    background-color: rgba(220, 20, 60, 0.9); /* Crimson red background */
    border-color: #DC143C;
    color: white;
}

/* Legacy support for old class names */
.win-message h3 {
    color: #00ff00;
    /* Bright green for wins */
}

.error-message h3 {
    color: #ff6b6b;
    /* Coral red for losses */
}

.info-message h3 {
    color: #ffcc00;
    /* Gold for ties */
}

/* Responsive adjustments */
@media (max-width: 900px) {
    .card, .cell, .pile, .empty-slot {
        width: 80px;
        height: 115px;
    }

    .column .card {
        margin-bottom: -88px; /* Adjust overlap */
    }

    .column .card:last-child {
        margin-bottom: 0;
    }
}

