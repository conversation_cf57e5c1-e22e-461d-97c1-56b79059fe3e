import os
import uuid
from datetime import datetime
from flask import Flask, render_template, redirect, url_for, session
from playcard import get_card_name
import blackjack, freecell


SUPPORTED_GAMES = {'blackjack': blackjack, 'freecell': freecell}
app = Flask(__name__)
# Generate a random secret key for the session
app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))

def add_log_entry(session, message):
    """添加日志条目到会话中"""
    if 'game_log' not in session:
        session['game_log'] = []

    timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
    log_entry = f"{timestamp} {message}"
    session['game_log'].append(log_entry)

    # 限制日志条目数量，避免会话过大
    if len(session['game_log']) > 100:
        session['game_log'] = session['game_log'][-100:]

    session.modified = True

def add_game_action_log(session, cur_game, action, old_state, new_state):
    """根据游戏类型和操作添加相应的日志"""
    if cur_game == 'blackjack':
        add_blackjack_log(session, action, old_state, new_state)
    elif cur_game == 'freecell':
        add_freecell_log(session, action, old_state, new_state)

def add_blackjack_log(session, action, old_state, new_state):
    """添加二十一点游戏日志"""
    if action == 'hit':
        if new_state.get('player_hand') and old_state.get('player_hand'):
            new_card = new_state['player_hand'][-1]
            card_name = get_card_name(new_card)
            add_log_entry(session, f"玩家要牌并获得{card_name}。")

            if new_state.get('player_value', 0) > 21:
                add_log_entry(session, "玩家爆牌并输掉游戏。")

    elif action == 'stand':
        add_log_entry(session, "玩家停牌。")

        if new_state.get('dealer_hand'):
            dealer_hidden_card = new_state['dealer_hand'][1] if len(new_state['dealer_hand']) > 1 else None
            if dealer_hidden_card:
                hidden_card_name = get_card_name(dealer_hidden_card)
                add_log_entry(session, f"庄家的暗牌是{hidden_card_name}。")

        # 检查庄家是否要牌
        if old_state.get('dealer_hand') and new_state.get('dealer_hand'):
            old_dealer_count = len(old_state['dealer_hand'])
            new_dealer_count = len(new_state['dealer_hand'])

            if new_dealer_count > old_dealer_count:
                for i in range(old_dealer_count, new_dealer_count):
                    new_card = new_state['dealer_hand'][i]
                    card_name = get_card_name(new_card)
                    add_log_entry(session, f"庄家获得{card_name}。")

        # 游戏结果
        if new_state.get('game_over'):
            dealer_value = new_state.get('dealer_value', 0)
            player_value = new_state.get('player_value', 0)

            if dealer_value > 21:
                add_log_entry(session, "庄家爆牌。玩家获胜。")
            elif player_value > dealer_value:
                add_log_entry(session, "玩家获胜。")
            elif player_value == dealer_value:
                add_log_entry(session, "平局。")
            else:
                add_log_entry(session, "庄家获胜。")

def add_freecell_log(session, action, old_state, new_state):
    """添加空当接龙游戏日志"""
    if action.startswith('tt'):  # 列到列
        source_col = int(action[2])
        target_col = int(action[3])
        add_log_entry(session, f"列区{source_col}的顶牌移动到列区{target_col}。")

    elif action.startswith('tc'):  # 列到单元格
        source_col = int(action[2])
        target_cell = int(action[3])
        add_log_entry(session, f"列区{source_col}的顶牌移动到单元格{target_cell}。")

    elif action.startswith('ct'):  # 单元格到列
        source_cell = int(action[2])
        target_col = int(action[3])
        add_log_entry(session, f"单元格{source_cell}的牌移动到列区{target_col}。")

    elif action.startswith('tf'):  # 列到基础堆
        source_col = int(action[2])
        add_log_entry(session, f"列区{source_col}的顶牌移动到基础堆。")

    elif action.startswith('cf'):  # 单元格到基础堆
        source_cell = int(action[2])
        add_log_entry(session, f"单元格{source_cell}的牌移动到基础堆。")

    # 检查获胜条件
    if new_state.get('game_over') and not old_state.get('game_over'):
        add_log_entry(session, "恭喜！您获胜了！")

@app.route('/')
def index():
    return redirect(url_for('game'))

@app.route('/select')
def select():
    session.setdefault('session_id', uuid.uuid4().hex)
    return render_template('select.html', cur_game=session.get('cur_game', ''))

@app.route('/new_game')
def new_game():
    cur_game = session.get('cur_game', '')
    if cur_game in SUPPORTED_GAMES:
        SUPPORTED_GAMES[cur_game].new_game(session)

        # 添加新游戏日志
        if cur_game == 'blackjack':
            add_log_entry(session, "开始新的二十一点游戏。")
            game_state = session.get('game_state', {})
            if game_state:
                player_cards = [get_card_name(card) for card in game_state.get('player_hand', [])]
                dealer_cards = [get_card_name(card) for card in game_state.get('dealer_hand', [])]
                add_log_entry(session, f"庄家获得{dealer_cards[0]}和另一张暗牌。玩家获得{player_cards[0]}和{player_cards[1]}。")
        elif cur_game == 'freecell':
            add_log_entry(session, "开始新的空当接龙游戏。")
            game_state = session.get('game_state', {})
            if game_state:
                tableau = game_state.get('tableau', [])
                total_cards = sum(len(col) for col in tableau)
                add_log_entry(session, f"52张牌已分发到8个列区中，游戏开始。")

        session.modified = True
        return redirect(url_for('game'))
    else:
        return redirect(url_for('select'))

@app.route('/game')
def game():
    session.setdefault('session_id', uuid.uuid4().hex)
    cur_game = session.get('cur_game', '')
    game_state = session.get('game_state', {})
    if cur_game in SUPPORTED_GAMES and game_state:
        return render_template(f'{cur_game}.html',
                               game_state=game_state)
    else:
        return redirect(url_for('select'))

@app.route('/game_update/<action>')
def game_update(action):
    cur_game = session.get('cur_game', '')
    if cur_game in SUPPORTED_GAMES:
        # 记录操作前的状态用于日志
        old_game_state = session.get('game_state', {}).copy() if session.get('game_state') else {}

        SUPPORTED_GAMES[cur_game].game_update(session, action)

        # 添加游戏操作日志
        new_game_state = session.get('game_state', {})
        add_game_action_log(session, cur_game, action, old_game_state, new_game_state)

        session.modified = True
        return redirect(url_for('game'))
    else:
        return redirect(url_for('select'))


@app.route('/select_game/<target_game>')
def select_game(target_game):
    if target_game in SUPPORTED_GAMES:
        session['cur_game'] = target_game
        SUPPORTED_GAMES[target_game].new_game(session)

        # 添加新游戏日志
        if target_game == 'blackjack':
            add_log_entry(session, "选择二十一点游戏。")
            game_state = session.get('game_state', {})
            if game_state:
                player_cards = [get_card_name(card) for card in game_state.get('player_hand', [])]
                dealer_cards = [get_card_name(card) for card in game_state.get('dealer_hand', [])]
                add_log_entry(session, f"新二十一点游戏。庄家获得{dealer_cards[0]}和另一张牌。玩家获得{player_cards[0]}和{player_cards[1]}。")
        elif target_game == 'freecell':
            add_log_entry(session, "选择空当接龙游戏。")
            game_state = session.get('game_state', {})
            if game_state:
                tableau = game_state.get('tableau', [])
                total_cards = sum(len(col) for col in tableau)
                add_log_entry(session, f"新空当接龙游戏开始，共{total_cards}张牌分布在8个列区中。")

        session.modified = True
        return redirect(url_for('game'))
    else:
        return render_template('about.html', supported=False)


@app.route('/rules')
def rules():
    return render_template('rules.html',
                           cur_game=session.get('cur_game', ''))

@app.route('/log')
def log():
    session.setdefault('session_id', uuid.uuid4().hex)
    game_log = session.get('game_log', [])
    log_text = '\n'.join(game_log) if game_log else '暂无游戏日志。'
    return render_template('userlog.html', log=log_text)

@app.route('/about')
def about():
    return render_template('about.html', supported=True)


@app.context_processor
def utility_processor():
    # Make the `get_card_name` function available in all templates
    return dict(get_card_name=get_card_name, enumerate=enumerate)


if __name__ == '__main__':
    app.run(port=80)