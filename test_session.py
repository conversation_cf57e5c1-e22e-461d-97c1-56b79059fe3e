#!/usr/bin/env python3

# Test script to check session and logging functionality
from flask import Flask, session
import sys
import os

# Add current directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask_app import app, add_log_entry

def test_logging():
    with app.test_client() as client:
        with client.session_transaction() as sess:
            # Test adding log entries
            print("Testing log functionality...")
            
            # Add some test log entries
            add_log_entry(sess, "测试日志条目1")
            add_log_entry(sess, "测试日志条目2")
            add_log_entry(sess, "测试日志条目3")
            
            # Check if logs were added
            game_log = sess.get('game_log', [])
            print(f"Log entries count: {len(game_log)}")
            
            for i, entry in enumerate(game_log):
                print(f"Log {i+1}: {entry}")
            
            # Test log page
            response = client.get('/log')
            print(f"\nLog page status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                if "测试日志条目1" in content:
                    print("✓ Log content found in response")
                else:
                    print("✗ Log content not found in response")
                    print("Response preview:")
                    print(content[:500])
            else:
                print("✗ Failed to get log page")

def test_rules():
    with app.test_client() as client:
        print("\n" + "="*50)
        print("Testing rules functionality...")
        
        # Test rules without game selected
        response = client.get('/rules')
        print(f"Rules page status (no game): {response.status_code}")
        
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            if "请先选择一个游戏" in content:
                print("✓ Default rules message found")
            else:
                print("✗ Default rules message not found")
                print("Response preview:")
                print(content[:500])
        
        # Test rules with blackjack selected
        with client.session_transaction() as sess:
            sess['cur_game'] = 'blackjack'
        
        response = client.get('/rules')
        print(f"Rules page status (blackjack): {response.status_code}")
        
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            if "二十一点游戏规则" in content:
                print("✓ Blackjack rules found")
            else:
                print("✗ Blackjack rules not found")
                print("Response preview:")
                print(content[:500])

if __name__ == "__main__":
    test_logging()
    test_rules()
    print("\nTesting complete!")
