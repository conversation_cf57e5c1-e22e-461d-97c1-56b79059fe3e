import random
from playcard import make_deck, get_card_name

CARD_VALUES = {
    'A': 11,
    '2': 2,
    '3': 3,
    '4': 4,
    '5': 5,
    '6': 6,
    '7': 7,
    '8': 8,
    '9': 9,
    'T': 10,
    'J': 10,
    'Q': 10,
    'K': 10,
}


def calculate_hand_value(hand):
    """计算手牌总点数，自动处理A牌的1/11点逻辑"""
    value, aces = 0, 0
    for card in hand:
        rank = card[0]
        value += CARD_VALUES[rank]
        aces += rank == 'A'  # 统计A牌数量

    # 当总点数超过21时，将A牌从11点调整为1点
    while value > 21 and aces > 0:
        value -= 10
        aces -= 1

    return value


def new_game(session):
    """初始化新游戏，创建牌组、发牌并设置初始状态"""
    deck = make_deck()
    random.shuffle(deck)

    player_hand = [deck.pop(), deck.pop()]
    dealer_hand = [deck.pop(), deck.pop()]

    session['game_state'] = {
        'deck': deck,
        'player_hand': player_hand,
        'dealer_hand': dealer_hand,
        'player_value': calculate_hand_value(player_hand),
        'dealer_value': calculate_hand_value([dealer_hand[0]]),  # 初始只计算明牌点数
        'game_over': False,
        'message': '',
        'message_class': 'info'
    }

    return session['game_state']


def game_update(session, action):
    """处理玩家动作并更新游戏状态"""
    game_state = session.get('game_state', {})
    if not game_state:
        return new_game(session)

    if game_state['game_over']:
        return game_state

    if action == 'hit':
        new_card = game_state['deck'].pop()
        game_state['player_hand'].append(new_card)
        game_state['player_value'] = calculate_hand_value(game_state['player_hand'])

        if game_state['player_value'] > 21:
            game_state['game_over'] = True
            # 玩家爆牌时，更新为庄家完整点数
            game_state['dealer_value'] = calculate_hand_value(game_state['dealer_hand'])
            game_state['message'] = "您爆牌了！您输了。"
            game_state['message_class'] = "error"
        else:
            game_state['message'] = ""
            game_state['message_class'] = "info"

    elif action == 'stand':
        dealer_hand = game_state['dealer_hand']
        dealer_value = calculate_hand_value(dealer_hand)

        # 庄家抽牌直到点数≥17
        while dealer_value < 17:
            new_card = game_state['deck'].pop()
            dealer_hand.append(new_card)
            dealer_value = calculate_hand_value(dealer_hand)

        player_val = game_state['player_value']
        dealer_val = dealer_value

        game_state['game_over'] = True

        if dealer_val > 21:
            game_state['message'] = "庄家爆牌！您获胜！"
            game_state['message_class'] = "success"
        elif player_val > dealer_val:
            game_state['message'] = "您获胜！"
            game_state['message_class'] = "success"
        elif player_val == dealer_val:
            game_state['message'] = "平局！"
            game_state['message_class'] = "info"
        else:
            game_state['message'] = "庄家获胜！"
            game_state['message_class'] = "error"

        # 更新游戏结束时庄家的实际点数
        game_state['dealer_value'] = calculate_hand_value(game_state['dealer_hand'])

    session.modified = True
    return game_state